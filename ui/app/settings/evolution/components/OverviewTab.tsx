"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { 
  Activity, 
  Brain, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp, 
  Settings,
  ExternalLink
} from "lucide-react";

interface OverviewTabProps {
  onSettingsChange: (hasChanges: boolean) => void;
}

export function OverviewTab({ onSettingsChange }: OverviewTabProps) {
  const [evolutionEnabled, setEvolutionEnabled] = useState(true);
  const [autoOptimization, setAutoOptimization] = useState(false);
  const [systemStatus, setSystemStatus] = useState({
    memoryEngine: "healthy",
    vectorStore: "healthy", 
    evolutionService: "warning",
    promptSystem: "healthy"
  });

  // Mock metrics data
  const [metrics, setMetrics] = useState({
    learningEfficiency: 78,
    memoryQuality: 92,
    operationSuccess: 85,
    systemUptime: 99.7
  });

  const handleToggleEvolution = (enabled: boolean) => {
    setEvolutionEnabled(enabled);
    onSettingsChange(true);
  };

  const handleToggleAutoOptimization = (enabled: boolean) => {
    setAutoOptimization(enabled);
    onSettingsChange(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy": return "text-green-400";
      case "warning": return "text-yellow-400";
      case "error": return "text-red-400";
      default: return "text-zinc-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy": return <CheckCircle className="h-4 w-4" />;
      case "warning": return <AlertTriangle className="h-4 w-4" />;
      case "error": return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* System Status Overview */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Current status of evolution intelligence components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(systemStatus).map(([component, status]) => (
              <div key={component} className="flex items-center justify-between p-3 rounded-lg bg-zinc-800/50">
                <div className="flex items-center gap-2">
                  <span className={getStatusColor(status)}>
                    {getStatusIcon(status)}
                  </span>
                  <span className="text-sm font-medium capitalize">
                    {component.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </div>
                <Badge 
                  variant={status === "healthy" ? "default" : status === "warning" ? "secondary" : "destructive"}
                  className="text-xs"
                >
                  {status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Metrics
          </CardTitle>
          <CardDescription>
            Current system performance indicators
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {Object.entries(metrics).map(([metric, value]) => (
              <div key={metric} className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="text-sm font-medium capitalize">
                    {metric.replace(/([A-Z])/g, ' $1').trim()}
                  </Label>
                  <span className="text-sm font-semibold">{value}%</span>
                </div>
                <Progress 
                  value={value} 
                  className="h-2"
                  // @ts-ignore
                  style={{
                    '--progress-background': value > 80 ? '#10b981' : value > 60 ? '#f59e0b' : '#ef4444'
                  }}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Evolution Control */}
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Evolution Intelligence
            </CardTitle>
            <CardDescription>
              Enable or disable memory evolution processing
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="evolution-enabled" className="text-sm font-medium">
                  Enable Evolution
                </Label>
                <p className="text-xs text-zinc-400">
                  Process memory updates, conflicts, and optimizations
                </p>
              </div>
              <Switch
                id="evolution-enabled"
                checked={evolutionEnabled}
                onCheckedChange={handleToggleEvolution}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="auto-optimization" className="text-sm font-medium">
                  Auto-Optimization
                </Label>
                <p className="text-xs text-zinc-400">
                  Automatically optimize memory structure and performance
                </p>
              </div>
              <Switch
                id="auto-optimization"
                checked={autoOptimization}
                onCheckedChange={handleToggleAutoOptimization}
                disabled={!evolutionEnabled}
              />
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common configuration tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              variant="outline" 
              className="w-full justify-start border-zinc-700 hover:bg-zinc-800"
            >
              <Brain className="mr-2 h-4 w-4" />
              Run Memory Analysis
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start border-zinc-700 hover:bg-zinc-800"
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              Generate Performance Report
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full justify-start border-zinc-700 hover:bg-zinc-800"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              View Evolution Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Summary */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle>Current Configuration Summary</CardTitle>
          <CardDescription>
            Overview of active evolution settings across all domains
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium text-zinc-200">Domain Settings</h4>
              <ul className="space-y-1 text-zinc-400">
                <li>• Active Domains: 3</li>
                <li>• Default Domain: General</li>
                <li>• Custom Rules: 12</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-zinc-200">Prompt Configuration</h4>
              <ul className="space-y-1 text-zinc-400">
                <li>• Fact Extraction: Custom</li>
                <li>• Memory Evolution: Default</li>
                <li>• Last Updated: 2 days ago</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-zinc-200">Advanced Settings</h4>
              <ul className="space-y-1 text-zinc-400">
                <li>• Batch Size: 50</li>
                <li>• Timeout: 30s</li>
                <li>• Retry Attempts: 3</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
