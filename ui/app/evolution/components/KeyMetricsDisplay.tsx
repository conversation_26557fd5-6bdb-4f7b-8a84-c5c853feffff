"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus, Brain, Target, Star, BarChart3 } from "lucide-react";
import { cn } from "@/lib/utils";

interface MetricCardProps {
  title: string;
  value: string | number;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    percentage: number;
    period: string;
  };
  icon: React.ReactNode;
  description?: string;
  color?: 'green' | 'blue' | 'purple' | 'orange';
}

function MetricCard({ title, value, trend, icon, description, color = 'blue' }: MetricCardProps) {
  const colorClasses = {
    green: 'text-green-400 bg-green-400/10 border-green-400/20',
    blue: 'text-blue-400 bg-blue-400/10 border-blue-400/20',
    purple: 'text-purple-400 bg-purple-400/10 border-purple-400/20',
    orange: 'text-orange-400 bg-orange-400/10 border-orange-400/20',
  };

  const getTrendIcon = () => {
    switch (trend?.direction) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />;
      case 'down':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return <Minus className="h-3 w-3" />;
    }
  };

  const getTrendColor = () => {
    switch (trend?.direction) {
      case 'up':
        return 'text-green-400';
      case 'down':
        return 'text-red-400';
      default:
        return 'text-zinc-400';
    }
  };

  return (
    <Card className={cn('border', colorClasses[color])}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-zinc-300">{title}</CardTitle>
        <div className={cn('p-2 rounded-lg', colorClasses[color])}>
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-white mb-1">{value}</div>
        {description && (
          <p className="text-xs text-zinc-400 mb-2">{description}</p>
        )}
        {trend && (
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className={cn('text-xs', getTrendColor(), 'border-current')}>
              {getTrendIcon()}
              {trend.percentage}%
            </Badge>
            <span className="text-xs text-zinc-400">vs {trend.period}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function KeyMetricsDisplay() {
  // Mock data - in real implementation, this would come from API
  const metrics = {
    learningEfficiency: {
      value: '87.3%',
      trend: { direction: 'up' as const, percentage: 5.2, period: 'last week' },
      description: 'Memory consolidation rate'
    },
    conflictResolution: {
      value: '94.1%',
      trend: { direction: 'up' as const, percentage: 2.8, period: 'last week' },
      description: 'Successful conflict handling'
    },
    memoryQuality: {
      value: '8.7/10',
      trend: { direction: 'neutral' as const, percentage: 0.3, period: 'last week' },
      description: 'Average quality score'
    },
    operationDistribution: {
      value: '1,247',
      trend: { direction: 'up' as const, percentage: 12.5, period: 'last week' },
      description: 'Total operations today'
    }
  };

  return (
    <Card className="border-zinc-800 bg-zinc-950/50">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white">Key Metrics</CardTitle>
        <p className="text-sm text-zinc-400">
          Real-time performance indicators for memory evolution operations
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard
            title="Learning Efficiency"
            value={metrics.learningEfficiency.value}
            trend={metrics.learningEfficiency.trend}
            icon={<Brain className="h-4 w-4" />}
            description={metrics.learningEfficiency.description}
            color="green"
          />
          
          <MetricCard
            title="Conflict Resolution"
            value={metrics.conflictResolution.value}
            trend={metrics.conflictResolution.trend}
            icon={<Target className="h-4 w-4" />}
            description={metrics.conflictResolution.description}
            color="blue"
          />
          
          <MetricCard
            title="Memory Quality Score"
            value={metrics.memoryQuality.value}
            trend={metrics.memoryQuality.trend}
            icon={<Star className="h-4 w-4" />}
            description={metrics.memoryQuality.description}
            color="purple"
          />
          
          <MetricCard
            title="Operation Distribution"
            value={metrics.operationDistribution.value}
            trend={metrics.operationDistribution.trend}
            icon={<BarChart3 className="h-4 w-4" />}
            description={metrics.operationDistribution.description}
            color="orange"
          />
        </div>
      </CardContent>
    </Card>
  );
}