"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Download, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ReferenceLine } from 'recharts';
import { useEvolutionAnalytics, TimelineDataPoint } from "@/hooks/useEvolutionAnalytics";
import { cn } from "@/lib/utils";

type TimeframePeriod = '24h' | '30d' | '12w' | '12m';

interface TimeframeOption {
  value: TimeframePeriod;
  label: string;
  description: string;
}

const timeframeOptions: TimeframeOption[] = [
  { value: '24h', label: '24H', description: 'Last 24 hours' },
  { value: '30d', label: '30D', description: 'Last 30 days' },
  { value: '12w', label: '12W', description: 'Last 12 weeks' },
  { value: '12m', label: '12M', description: 'Last 12 months' }
];

// Linear regression for trend lines
const calculateTrendLine = (data: TimelineDataPoint[], key: keyof TimelineDataPoint): number[] => {
  const n = data.length;
  if (n < 2) return [];

  const xValues = data.map((_, index) => index);
  const yValues = data.map(d => typeof d[key] === 'number' ? d[key] as number : 0);

  const sumX = xValues.reduce((a, b) => a + b, 0);
  const sumY = yValues.reduce((a, b) => a + b, 0);
  const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
  const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;

  return xValues.map(x => slope * x + intercept);
};

// Anomaly detection - simple threshold-based
const detectAnomalies = (data: TimelineDataPoint[]): number[] => {
  const values = data.map(d => d.total);
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const stdDev = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length);
  const threshold = mean + 2 * stdDev; // 2 standard deviations

  return values.map(val => val > threshold ? val : 0);
};

export function EvolutionTimeline() {
  const [selectedTimeframe, setSelectedTimeframe] = useState<TimeframePeriod>('30d');
  const [timelineData, setTimelineData] = useState<TimelineDataPoint[]>([]);
  const [zoomDomain, setZoomDomain] = useState<[number, number] | null>(null);
  const [selectedPoint, setSelectedPoint] = useState<TimelineDataPoint | null>(null);
  const chartRef = useRef<any>(null);

  const { isLoading, error, fetchTimelineData, exportToCsv, fetchAnalytics } = useEvolutionAnalytics();

  useEffect(() => {
    const loadTimelineData = async () => {
      try {
        const data = await fetchTimelineData(selectedTimeframe);
        setTimelineData(data);
        setZoomDomain(null); // Reset zoom when timeframe changes
      } catch (err) {
        console.error('Failed to load timeline data:', err);
      }
    };

    loadTimelineData();
  }, [selectedTimeframe, fetchTimelineData]);

  const handleExportCsv = async () => {
    try {
      const analyticsData = await fetchAnalytics({ size: 1000 });
      exportToCsv(analyticsData.items, `evolution-timeline-${selectedTimeframe}-${new Date().toISOString().split('T')[0]}.csv`);
    } catch (err) {
      console.error('Failed to export CSV:', err);
    }
  };

  const handleZoomIn = () => {
    if (timelineData.length > 0) {
      const dataLength = timelineData.length;
      const start = Math.floor(dataLength * 0.25);
      const end = Math.floor(dataLength * 0.75);
      setZoomDomain([start, end]);
    }
  };

  const handleZoomOut = () => {
    setZoomDomain(null);
  };

  const handlePointClick = (data: any) => {
    if (data && data.activePayload && data.activePayload[0]) {
      setSelectedPoint(data.activePayload[0].payload);
    }
  };

  // Calculate trend lines and anomalies
  const addTrend = calculateTrendLine(timelineData, 'ADD');
  const updateTrend = calculateTrendLine(timelineData, 'UPDATE');
  const deleteTrend = calculateTrendLine(timelineData, 'DELETE');
  const noopTrend = calculateTrendLine(timelineData, 'NOOP');
  const anomalies = detectAnomalies(timelineData);

  // Enhance data with trend lines and anomalies
  const enhancedData = timelineData.map((point, index) => ({
    ...point,
    addTrend: addTrend[index] || 0,
    updateTrend: updateTrend[index] || 0,
    deleteTrend: deleteTrend[index] || 0,
    noopTrend: noopTrend[index] || 0,
    isAnomaly: anomalies[index] > 0,
    displayTime: formatTimeDisplay(point.timestamp, selectedTimeframe)
  }));

  function formatTimeDisplay(timestamp: string, timeframe: TimeframePeriod): string {
    const date = new Date(timestamp);
    switch (timeframe) {
      case '24h':
        return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
      case '30d':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case '12w':
        return `W${Math.ceil(date.getDate() / 7)} ${date.toLocaleDateString('en-US', { month: 'short' })}`;
      case '12m':
        return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      default:
        return timestamp;
    }
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-zinc-900 border border-zinc-700 rounded-lg p-3 shadow-lg">
          <p className="text-white font-medium mb-2">{label}</p>
          <div className="space-y-1">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-zinc-300">{entry.name}:</span>
                <span className="text-white font-medium">{entry.value}</span>
              </div>
            ))}
          </div>
          {data.isAnomaly && (
            <Badge variant="destructive" className="mt-2 text-xs">
              Anomaly Detected
            </Badge>
          )}
        </div>
      );
    }
    return null;
  };

  if (error) {
    return (
      <Card className="border-zinc-800 bg-zinc-950/50">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Evolution Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-red-400">
            Error loading timeline data: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-zinc-800 bg-zinc-950/50">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Evolution Timeline
        </CardTitle>
        <p className="text-sm text-zinc-400 mb-4">
          Interactive timeline showing memory operation trends with anomaly detection
        </p>

        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          {/* Timeframe Selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-zinc-400">Period:</span>
            <div className="flex gap-1">
              {timeframeOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedTimeframe === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTimeframe(option.value)}
                  className={cn(
                    "text-xs",
                    selectedTimeframe === option.value
                      ? "bg-blue-600 hover:bg-blue-700"
                      : "border-zinc-700 hover:bg-zinc-800"
                  )}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomIn}
              disabled={isLoading || timelineData.length === 0}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomOut}
              disabled={isLoading || !zoomDomain}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
              disabled={isLoading}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportCsv}
              disabled={isLoading || timelineData.length === 0}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <Download className="h-4 w-4" />
              CSV
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-zinc-400">Loading timeline data...</div>
          </div>
        ) : timelineData.length === 0 ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-zinc-400">No data available for selected timeframe</div>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                ref={chartRef}
                data={enhancedData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                onClick={handlePointClick}
                {...(zoomDomain && {
                  domain: { x: zoomDomain }
                })}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis
                  dataKey="displayTime"
                  stroke="#9CA3AF"
                  fontSize={12}
                  {...(zoomDomain && {
                    domain: zoomDomain
                  })}
                />
                <YAxis stroke="#9CA3AF" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />

                {/* Main operation lines */}
                <Line
                  type="monotone"
                  dataKey="ADD"
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#10B981', strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="UPDATE"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3B82F6', strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="DELETE"
                  stroke="#EF4444"
                  strokeWidth={2}
                  dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#EF4444', strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="NOOP"
                  stroke="#F59E0B"
                  strokeWidth={2}
                  dot={{ fill: '#F59E0B', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#F59E0B', strokeWidth: 2 }}
                />

                {/* Trend lines (dashed) */}
                <Line
                  type="monotone"
                  dataKey="addTrend"
                  stroke="#10B981"
                  strokeWidth={1}
                  strokeDasharray="5 5"
                  dot={false}
                  activeDot={false}
                  name="ADD Trend"
                />
                <Line
                  type="monotone"
                  dataKey="updateTrend"
                  stroke="#3B82F6"
                  strokeWidth={1}
                  strokeDasharray="5 5"
                  dot={false}
                  activeDot={false}
                  name="UPDATE Trend"
                />

                {/* Anomaly reference lines */}
                {enhancedData.map((point, index) =>
                  point.isAnomaly ? (
                    <ReferenceLine
                      key={`anomaly-${index}`}
                      x={point.displayTime}
                      stroke="#DC2626"
                      strokeDasharray="2 2"
                      strokeWidth={1}
                    />
                  ) : null
                )}
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Selected Point Details */}
        {selectedPoint && (
          <div className="mt-4 p-4 bg-zinc-900/50 rounded-lg border border-zinc-700">
            <h4 className="text-white font-medium mb-2">
              Data Point Details - {selectedPoint.displayTime}
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-green-400">ADD:</span>
                <span className="text-white ml-2">{selectedPoint.ADD}</span>
              </div>
              <div>
                <span className="text-blue-400">UPDATE:</span>
                <span className="text-white ml-2">{selectedPoint.UPDATE}</span>
              </div>
              <div>
                <span className="text-red-400">DELETE:</span>
                <span className="text-white ml-2">{selectedPoint.DELETE}</span>
              </div>
              <div>
                <span className="text-yellow-400">NOOP:</span>
                <span className="text-white ml-2">{selectedPoint.NOOP}</span>
              </div>
            </div>
            <div className="mt-2 text-sm">
              <span className="text-zinc-400">Total Operations:</span>
              <span className="text-white ml-2">{selectedPoint.total}</span>
              {selectedPoint.isAnomaly && (
                <Badge variant="destructive" className="ml-4 text-xs">
                  Anomaly
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}