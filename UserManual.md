# Memory Master v2 - User Manual

This comprehensive user guide will walk you through all aspects of using Memory Master v2, from initial setup to advanced memory management operations.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Account Management](#account-management)
3. [MCP Integration](#mcp-integration)
4. [Web Dashboard Usage](#web-dashboard-usage)
5. [Memory Operations](#memory-operations)
6. [API Usage](#api-usage)
7. [Evolution Intelligence](#evolution-intelligence)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### First-Time Setup

1. **Verify Installation**: Ensure Memory Master v2 is running by visiting:
   - Web Dashboard: http://localhost:3000
   - API Documentation: http://localhost:8765/docs

2. **Check System Health**: Navigate to the dashboard and verify all services are green.

3. **Create Your First Memory**: Use the web interface or MCP tools to add a test memory.

### Understanding the System

Memory Master v2 consists of three main components:
- **Memory Storage**: Your personal memories stored with vector embeddings
- **Applications**: Different AI tools that can access your memories (Claude, VS Code, etc.)
- **Evolution Intelligence**: Automatic memory optimization system

## Account Management

### User Accounts

Memory Master v2 uses a simple user identification system:

1. **User ID**: Set in your environment variables (`USER` in api/.env)
2. **Default User**: Automatically created on first startup
3. **No Passwords**: Authentication is handled through environment configuration

### Managing Applications

Each AI tool that connects to your memory system is treated as an "application":

**Creating Applications**:
1. Navigate to the "Apps" section in the web dashboard
2. Click "Add New App"
3. Provide a descriptive name (e.g., "Claude Desktop", "VS Code")
4. Configure access permissions

**Application States**:
- **Active**: App can read and write memories
- **Paused**: App can only read existing memories
- **Archived**: App has no access to memories

## MCP Integration

### Claude Desktop Integration

**Step 1: Configure Claude Desktop**

Add this configuration to your Claude Desktop settings:

```json
{
  "mcpServers": {
    "memory-master": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_API_URL": "http://localhost:8765",
        "USER_ID": "your-user-id"
      }
    }
  }
}
```

**Step 2: Restart Claude Desktop**

Close and reopen Claude Desktop to load the new MCP server.

**Step 3: Verify Connection**

In a new Claude conversation, try:
```
Please search my memories for "test"
```

### VS Code Integration

**Step 1: Install MCP Extension**

Install the Model Context Protocol extension for VS Code.

**Step 2: Configure Settings**

Add to your VS Code settings.json:
```json
{
  "mcp.servers": {
    "memory-master": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_API_URL": "http://localhost:8765"
      }
    }
  }
}
```

### Other MCP-Compatible Tools

Memory Master v2 works with any MCP-compatible application:
- **Cursor**: Follow VS Code instructions
- **Windsurf**: Use similar MCP configuration
- **Custom Tools**: Implement MCP client using the standard protocol

## Web Dashboard Usage

### Dashboard Overview

The web dashboard provides a comprehensive interface for managing your memory system:

**Main Sections**:
1. **Dashboard**: System overview and health status
2. **Memories**: Browse, search, and manage your memories
3. **Apps**: Manage connected applications
4. **Analytics**: View memory usage statistics
5. **Settings**: Configure system behavior

### Memory Management

**Viewing Memories**:
1. Navigate to the "Memories" section
2. Use filters to find specific memories:
   - By application
   - By date range
   - By content type
   - By state (active, paused, archived)

**Editing Memories**:
1. Click on any memory to view details
2. Edit content, metadata, or state
3. Save changes to update the memory

**Deleting Memories**:
1. Select memories to delete
2. Choose between:
   - **Soft Delete**: Mark as deleted (can be recovered)
   - **Hard Delete**: Permanently remove from system

### Application Management

**Monitoring App Usage**:
1. View memory access statistics per app
2. See recent activity and access patterns
3. Monitor memory creation and retrieval rates

**Configuring App Permissions**:
1. Set read/write permissions per app
2. Configure memory retention policies
3. Enable/disable evolution intelligence per app

## Memory Operations

### Adding Memories

**Via Web Dashboard**:
1. Click "Add Memory" button
2. Enter memory content
3. Add relevant metadata (tags, categories)
4. Select target application
5. Save the memory

**Via MCP Tools**:
```
Please remember: "I prefer using TypeScript for all new projects because it catches errors early and improves code maintainability."
```

**Via API**:
```bash
curl -X POST "http://localhost:8765/api/v1/memories" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Important project decision",
    "metadata_": {"project": "memory-master", "type": "decision"},
    "user_id": "your-user-id",
    "app_id": "your-app-id"
  }'
```

### Searching Memories

**Semantic Search**:
Memory Master v2 uses vector embeddings for intelligent search:

```
Find memories about "database optimization"
```

This will return memories related to database performance, even if they don't contain the exact phrase.

**Filtered Search**:
- Search within specific applications
- Filter by date ranges
- Search by memory state
- Filter by metadata tags

### Memory States

**Active**: Memory is available for retrieval and can be updated
**Paused**: Memory is read-only, cannot be modified
**Archived**: Memory is stored but not actively searched
**Deleted**: Memory is marked for deletion but not yet removed

## API Usage

### Authentication

API requests require either:
1. **Environment-based**: User ID from environment variables
2. **Header-based**: `X-User-ID` header in requests
3. **API Key**: Optional API key for additional security

### Core Endpoints

**Add Memory**:
```bash
POST /api/v1/memories
{
  "content": "Memory content",
  "metadata_": {"key": "value"},
  "user_id": "uuid",
  "app_id": "uuid"
}
```

**Search Memories**:
```bash
GET /api/v1/memories/search?query=search_term&limit=10
```

**List Memories**:
```bash
GET /api/v1/memories?app_id=uuid&limit=50&offset=0
```

**Update Memory**:
```bash
PUT /api/v1/memories/{memory_id}
{
  "content": "Updated content",
  "state": "active"
}
```

**Delete Memory**:
```bash
DELETE /api/v1/memories/{memory_id}?hard_delete=false
```

### MCP Tool Endpoints

**Available Tools**:
- `add_memories`: Store new memories
- `search_memory`: Semantic search
- `list_memories`: Browse memories
- `get_system_health`: System status
- `get_evolution_metrics`: Intelligence metrics

## Evolution Intelligence

### Understanding Evolution

Evolution Intelligence automatically optimizes your memory system by:
- **ADD**: Creating new memories from conversations
- **UPDATE**: Modifying existing memories with new information
- **DELETE**: Removing outdated or redundant memories
- **NOOP**: No operation needed

### Monitoring Evolution

**Via Web Dashboard**:
1. Navigate to "Analytics" section
2. View evolution statistics
3. See operation breakdown (ADD/UPDATE/DELETE/NOOP)
4. Monitor learning efficiency metrics

**Via API**:
```bash
GET /api/v1/evolution/metrics?timeframe=week
```

### Configuring Evolution

**Custom Prompts**:
Configure how the system extracts and processes memories:

```json
{
  "custom_fact_extraction_prompt": "Extract technical facts and decisions from this conversation...",
  "custom_update_memory_prompt": "Determine if existing memories should be updated..."
}
```

**Evolution Settings**:
- Enable/disable per application
- Set operation thresholds
- Configure learning parameters

## Best Practices

### Memory Content Guidelines

**Effective Memories**:
- Be specific and contextual
- Include temporal markers ("Today", "In project X")
- Link to actions and consequences
- Use relevant metadata tags

**Example Good Memory**:
```
"In the Memory Master v2 project, I discovered that using connection pooling with SQLAlchemy reduced database query time by 40%. This was implemented in the database service layer."
```

**Example Poor Memory**:
```
"Database optimization is good."
```

### Application Organization

**Separate by Context**:
- Create different apps for different projects
- Use descriptive names (e.g., "Project-Alpha-Claude", "Personal-VS-Code")
- Configure appropriate permissions per app

**Memory Hygiene**:
- Regularly review and clean up outdated memories
- Use the evolution intelligence to maintain quality
- Archive old project memories instead of deleting

### Performance Optimization

**For Best Performance**:
- Keep individual memories under 2000 words
- Use relevant metadata for faster filtering
- Regularly monitor system health
- Enable caching for frequently accessed memories

## Troubleshooting

### Common Issues

**Memory Not Saving**:
1. Check OpenAI API key configuration
2. Verify Qdrant vector store connectivity
3. Ensure sufficient disk space
4. Check application permissions

**Search Not Working**:
1. Verify vector embeddings are generated
2. Check search query format
3. Ensure memories are in "active" state
4. Test with simpler search terms

**MCP Connection Failed**:
1. Verify API server is running (http://localhost:8765)
2. Check MCP server configuration
3. Restart the client application
4. Review API server logs

**Slow Performance**:
1. Check system resource usage
2. Optimize database queries
3. Clear old cached data
4. Reduce concurrent operations

### Getting Help

**Log Files**:
- API Server: `docker-compose logs openmemory-mcp`
- UI: `docker-compose logs openmemory-ui`
- Vector Store: `docker-compose logs mem0_store`

**Health Checks**:
- System Health: http://localhost:8765/health
- Qdrant Status: http://localhost:6333/dashboard
- Database Connection: Check API logs

**Support Resources**:
- API Documentation: http://localhost:8765/docs
- System Metrics: Web dashboard analytics
- Developer Manual: See DeveloperManual.md

---

This user manual covers the essential operations for Memory Master v2. For technical details and development information, refer to the DeveloperManual.md.
