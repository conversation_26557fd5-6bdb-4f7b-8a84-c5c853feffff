from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..database import get_db
from ..models import (
    EvolutionConfiguration,
    ConfigurationVersion,
    NoopThreshold,
    EvolutionAnalytics,
    User,
    App
)
from ..schemas import (
    EvolutionConfigurationCreate,
    EvolutionConfigurationUpdate,
    EvolutionConfiguration as EvolutionConfigurationSchema,
    ConfigurationVersionCreate,
    ConfigurationVersion as ConfigurationVersionSchema,
    NoopThresholdCreate,
    NoopThresholdUpdate,
    NoopThreshold as NoopThresholdSchema,
    EvolutionAnalyticsCreate,
    EvolutionAnalytics as EvolutionAnalyticsSchema,
    PaginatedEvolutionConfigurationResponse,
    PaginatedEvolutionAnalyticsResponse,
    DomainTypeEnum
)
from ..auth import get_current_user
from ..auth.middleware import get_authenticated_user_context, AuthenticatedUser, DefaultUser
from typing import Union

router = APIRouter(prefix="/evolution-config", tags=["evolution-config"])


@router.post("/", response_model=EvolutionConfigurationSchema)
async def create_evolution_configuration(
    config_data: EvolutionConfigurationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new evolution configuration."""
    # Verify app exists if app_id is provided
    if config_data.app_id:
        app = db.query(App).filter(
            and_(App.id == config_data.app_id, App.user_id == current_user.id)
        ).first()
        if not app:
            raise HTTPException(status_code=404, detail="App not found")
    
    # Check if configuration already exists for this user/app/domain combination
    existing_config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.user_id == current_user.id,
            EvolutionConfiguration.app_id == config_data.app_id,
            EvolutionConfiguration.domain_type == config_data.domain_type
        )
    ).first()
    
    if existing_config:
        raise HTTPException(
            status_code=400,
            detail=f"Configuration for {config_data.domain_type} already exists"
        )
    
    # Create the configuration
    db_config = EvolutionConfiguration(
        user_id=current_user.id,
        app_id=config_data.app_id,
        domain_type=config_data.domain_type,
        fact_extraction_prompt=config_data.fact_extraction_prompt,
        memory_evolution_prompt=config_data.memory_evolution_prompt,
        is_active=config_data.is_active,
        metadata_=config_data.metadata_
    )
    
    db.add(db_config)
    db.flush()  # Get the ID
    
    # Create NOOP threshold if provided
    if config_data.noop_threshold:
        noop_threshold = NoopThreshold(
            config_id=db_config.id,
            similarity_threshold=config_data.noop_threshold.similarity_threshold,
            update_threshold=config_data.noop_threshold.update_threshold,
            content_length_min=config_data.noop_threshold.content_length_min,
            confidence_threshold=config_data.noop_threshold.confidence_threshold
        )
        db.add(noop_threshold)
    
    db.commit()
    db.refresh(db_config)
    
    return db_config


@router.get("/", response_model=PaginatedEvolutionConfigurationResponse)
async def get_evolution_configurations(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    app_id: Optional[UUID] = None,
    domain_type: Optional[DomainTypeEnum] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get evolution configurations for the current user."""
    query = db.query(EvolutionConfiguration).filter(
        EvolutionConfiguration.user_id == current_user.id
    )
    
    # Apply filters
    if app_id:
        query = query.filter(EvolutionConfiguration.app_id == app_id)
    if domain_type:
        query = query.filter(EvolutionConfiguration.domain_type == domain_type)
    if is_active is not None:
        query = query.filter(EvolutionConfiguration.is_active == is_active)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * size
    configs = query.offset(offset).limit(size).all()
    
    pages = (total + size - 1) // size
    
    return PaginatedEvolutionConfigurationResponse(
        items=configs,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{config_id}", response_model=EvolutionConfigurationSchema)
async def get_evolution_configuration(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific evolution configuration."""
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    return config


@router.put("/{config_id}", response_model=EvolutionConfigurationSchema)
async def update_evolution_configuration(
    config_id: UUID,
    config_data: EvolutionConfigurationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update an evolution configuration."""
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    # Store original data for versioning
    original_data = {
        "domain_type": config.domain_type,
        "fact_extraction_prompt": config.fact_extraction_prompt,
        "memory_evolution_prompt": config.memory_evolution_prompt,
        "is_active": config.is_active,
        "metadata_": config.metadata_
    }
    
    # Update fields
    update_data = config_data.dict(exclude_unset=True)
    changes = {}
    
    for field, value in update_data.items():
        if field == "noop_threshold":
            continue  # Handle separately
        if hasattr(config, field) and getattr(config, field) != value:
            changes[field] = {"old": getattr(config, field), "new": value}
            setattr(config, field, value)
    
    # Handle NOOP threshold update
    if config_data.noop_threshold:
        noop_threshold = db.query(NoopThreshold).filter(
            NoopThreshold.config_id == config_id
        ).first()
        
        if noop_threshold:
            # Update existing threshold
            threshold_data = config_data.noop_threshold.dict(exclude_unset=True)
            for field, value in threshold_data.items():
                if hasattr(noop_threshold, field) and getattr(noop_threshold, field) != value:
                    changes[f"noop_threshold.{field}"] = {
                        "old": getattr(noop_threshold, field),
                        "new": value
                    }
                    setattr(noop_threshold, field, value)
        else:
            # Create new threshold
            noop_threshold = NoopThreshold(
                config_id=config_id,
                **config_data.noop_threshold.dict()
            )
            db.add(noop_threshold)
            changes["noop_threshold"] = {"old": None, "new": config_data.noop_threshold.dict()}
    
    # Create version record if there are changes
    if changes:
        version = ConfigurationVersion(
            config_id=config_id,
            changes=changes,
            rollback_data=original_data,
            created_by=current_user.id
        )
        db.add(version)
    
    db.commit()
    db.refresh(config)
    
    return config


@router.delete("/{config_id}")
async def delete_evolution_configuration(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete an evolution configuration."""
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    db.delete(config)
    db.commit()
    
    return {"message": "Configuration deleted successfully"}


@router.get("/{config_id}/versions", response_model=List[ConfigurationVersionSchema])
async def get_configuration_versions(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get version history for a configuration."""
    # Verify config ownership
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    versions = db.query(ConfigurationVersion).filter(
        ConfigurationVersion.config_id == config_id
    ).order_by(ConfigurationVersion.created_at.desc()).all()
    
    return versions


@router.post("/analytics", response_model=EvolutionAnalyticsSchema)
async def create_evolution_analytics(
    analytics_data: EvolutionAnalyticsCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create evolution analytics record."""
    # Verify app exists if app_id is provided
    if analytics_data.app_id:
        app = db.query(App).filter(
            and_(App.id == analytics_data.app_id, App.user_id == current_user.id)
        ).first()
        if not app:
            raise HTTPException(status_code=404, detail="App not found")
    
    # Verify config exists if config_id is provided
    if analytics_data.config_id:
        config = db.query(EvolutionConfiguration).filter(
            and_(
                EvolutionConfiguration.id == analytics_data.config_id,
                EvolutionConfiguration.user_id == current_user.id
            )
        ).first()
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
    
    db_analytics = EvolutionAnalytics(
        user_id=current_user.id,
        app_id=analytics_data.app_id,
        config_id=analytics_data.config_id,
        operation_type=analytics_data.operation_type,
        confidence_score=analytics_data.confidence_score,
        similarity_score=analytics_data.similarity_score,
        reasoning=analytics_data.reasoning,
        processing_time_ms=analytics_data.processing_time_ms,
        metadata_=analytics_data.metadata_
    )
    
    db.add(db_analytics)
    db.commit()
    db.refresh(db_analytics)
    
    return db_analytics


@router.get("/analytics", response_model=PaginatedEvolutionAnalyticsResponse)
async def get_evolution_analytics(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    app_id: Optional[UUID] = None,
    config_id: Optional[UUID] = None,
    operation_type: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get evolution analytics for the current user."""
    query = db.query(EvolutionAnalytics).filter(
        EvolutionAnalytics.user_id == current_user.id
    )

    # Apply filters
    if app_id:
        query = query.filter(EvolutionAnalytics.app_id == app_id)
    if config_id:
        query = query.filter(EvolutionAnalytics.config_id == config_id)
    if operation_type:
        query = query.filter(EvolutionAnalytics.operation_type == operation_type)

    # Get total count
    total = query.count()

    # Apply pagination
    offset = (page - 1) * size
    analytics = query.order_by(EvolutionAnalytics.timestamp.desc()).offset(offset).limit(size).all()

    pages = (total + size - 1) // size

    return PaginatedEvolutionAnalyticsResponse(
        items=analytics,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/metrics")
async def get_key_metrics(
    timeframe: str = Query("week", regex="^(day|week|month|year)$"),
    auth_context: tuple[Union[AuthenticatedUser, DefaultUser], User] = Depends(get_authenticated_user_context)
):
    """Get key metrics for the evolution dashboard."""
    from datetime import datetime, timedelta
    from sqlalchemy import func

    auth_user, current_user = auth_context

    # Calculate date range based on timeframe
    now = datetime.utcnow()
    if timeframe == "day":
        start_date = now - timedelta(days=1)
        previous_start = start_date - timedelta(days=1)
    elif timeframe == "week":
        start_date = now - timedelta(weeks=1)
        previous_start = start_date - timedelta(weeks=1)
    elif timeframe == "month":
        start_date = now - timedelta(days=30)
        previous_start = start_date - timedelta(days=30)
    else:  # year
        start_date = now - timedelta(days=365)
        previous_start = start_date - timedelta(days=365)

    # Get database session
    from app.database import SessionLocal
    db = SessionLocal()

    try:
        # Get current period analytics
        current_query = db.query(EvolutionAnalytics).filter(
            EvolutionAnalytics.user_id == current_user.id,
            EvolutionAnalytics.timestamp >= start_date
        )

        # Get previous period analytics for trend calculation
        previous_query = db.query(EvolutionAnalytics).filter(
            EvolutionAnalytics.user_id == current_user.id,
            EvolutionAnalytics.timestamp >= previous_start,
            EvolutionAnalytics.timestamp < start_date
        )

        current_analytics = current_query.all()
        previous_analytics = previous_query.all()

        # Calculate current metrics
        total_operations = len(current_analytics)
        add_operations = sum(1 for op in current_analytics if op.operation_type == 'ADD')
        update_operations = sum(1 for op in current_analytics if op.operation_type == 'UPDATE')
        delete_operations = sum(1 for op in current_analytics if op.operation_type == 'DELETE')
        noop_operations = sum(1 for op in current_analytics if op.operation_type == 'NOOP')

        # Calculate previous metrics for trends
        prev_total = len(previous_analytics)
        prev_add = sum(1 for op in previous_analytics if op.operation_type == 'ADD')
        prev_update = sum(1 for op in previous_analytics if op.operation_type == 'UPDATE')
        prev_delete = sum(1 for op in previous_analytics if op.operation_type == 'DELETE')
        prev_noop = sum(1 for op in previous_analytics if op.operation_type == 'NOOP')

        # Calculate Learning Efficiency (intelligent operations vs basic ADD)
        intelligent_operations = update_operations + delete_operations + noop_operations
        learning_efficiency = (intelligent_operations / total_operations * 100) if total_operations > 0 else 0.0
        prev_intelligent = prev_update + prev_delete + prev_noop
        prev_learning_efficiency = (prev_intelligent / prev_total * 100) if prev_total > 0 else 0.0

        # Calculate Conflict Resolution (DELETE operations success rate)
        conflict_resolution = (delete_operations / total_operations * 100) if total_operations > 0 else 0.0
        prev_conflict_resolution = (prev_delete / prev_total * 100) if prev_total > 0 else 0.0

        # Calculate Memory Quality Score (average confidence)
        confidence_scores = [op.confidence_score for op in current_analytics if op.confidence_score is not None]
        memory_quality = (sum(confidence_scores) / len(confidence_scores) * 10) if confidence_scores else 0.0
        prev_confidence_scores = [op.confidence_score for op in previous_analytics if op.confidence_score is not None]
        prev_memory_quality = (sum(prev_confidence_scores) / len(prev_confidence_scores) * 10) if prev_confidence_scores else 0.0

        # Calculate trends
        def calculate_trend(current, previous):
            if previous == 0:
                return {"direction": "neutral", "percentage": 0.0}
            change = ((current - previous) / previous) * 100
            if change > 1:
                return {"direction": "up", "percentage": round(abs(change), 1)}
            elif change < -1:
                return {"direction": "down", "percentage": round(abs(change), 1)}
            else:
                return {"direction": "neutral", "percentage": round(abs(change), 1)}

        return {
            "learning_efficiency": {
                "value": f"{learning_efficiency:.1f}%",
                "trend": {
                    **calculate_trend(learning_efficiency, prev_learning_efficiency),
                    "period": f"last {timeframe}"
                },
                "description": "Memory consolidation rate"
            },
            "conflict_resolution": {
                "value": f"{conflict_resolution:.1f}%",
                "trend": {
                    **calculate_trend(conflict_resolution, prev_conflict_resolution),
                    "period": f"last {timeframe}"
                },
                "description": "Successful conflict handling"
            },
            "memory_quality": {
                "value": f"{memory_quality:.1f}/10",
                "trend": {
                    **calculate_trend(memory_quality, prev_memory_quality),
                    "period": f"last {timeframe}"
                },
                "description": "Average quality score"
            },
            "operation_distribution": {
                "value": str(total_operations),
                "trend": {
                    **calculate_trend(total_operations, prev_total),
                    "period": f"last {timeframe}"
                },
                "description": f"Total operations this {timeframe}"
            }
        }

    finally:
        db.close()