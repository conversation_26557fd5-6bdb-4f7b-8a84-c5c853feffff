# Memory Master v2 - Developer Manual

This technical documentation provides comprehensive information for developers contributing to or extending Memory Master v2.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [Folder Structure](#folder-structure)
4. [Development Setup](#development-setup)
5. [Core Components](#core-components)
6. [Evolution Intelligence](#evolution-intelligence)
7. [API Design](#api-design)
8. [Testing Strategy](#testing-strategy)
9. [Deployment](#deployment)
10. [Known Limitations](#known-limitations)
11. [Future Roadmap](#future-roadmap)
12. [Contributing Guidelines](#contributing-guidelines)

## System Architecture

### High-Level Architecture

Memory Master v2 follows a microservices architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │   API Server    │    │  Vector Store   │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Qdrant)      │
│   Port: 3000    │    │   Port: 8765    │    │   Port: 6333    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Database      │
                       │   (Supabase)    │
                       │   Port: 54322   │
                       └─────────────────┘
```

### Component Responsibilities

**API Server (FastAPI)**:
- RESTful API endpoints
- MCP server implementation
- Business logic and services
- Authentication and authorization
- Database operations

**Vector Store (Qdrant)**:
- Vector embeddings storage
- Semantic search capabilities
- Memory similarity calculations
- High-performance retrieval

**Database (Supabase PostgreSQL)**:
- Relational data storage
- User and app management
- Memory metadata
- Evolution intelligence tracking

**Web UI (Next.js)**:
- User interface for memory management
- System monitoring dashboard
- Configuration management
- Analytics visualization

## Database Schema

### Core Tables

**Users Table** (`memory_master.users`):
```sql
CREATE TABLE memory_master.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR UNIQUE NOT NULL,
    name VARCHAR,
    email VARCHAR UNIQUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    supabase_user_id UUID UNIQUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_sign_in_at TIMESTAMP
);
```

**Apps Table** (`memory_master.apps`):
```sql
CREATE TABLE memory_master.apps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES memory_master.users(id),
    name VARCHAR NOT NULL,
    description VARCHAR,
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(owner_id, name)
);
```

**Memories Table** (`memory_master.memories`):
```sql
CREATE TABLE memory_master.memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES memory_master.users(id),
    app_id UUID REFERENCES memory_master.apps(id),
    content TEXT NOT NULL,
    vector VARCHAR,
    metadata JSONB DEFAULT '{}',
    state VARCHAR DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    archived_at TIMESTAMP,
    deleted_at TIMESTAMP
);
```

### Evolution Intelligence Tables

**Evolution Operations** (`memory_master.evolution_operations`):
```sql
CREATE TABLE memory_master.evolution_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES memory_master.users(id),
    app_id UUID REFERENCES memory_master.apps(id),
    memory_id UUID REFERENCES memory_master.memories(id),
    operation_type VARCHAR NOT NULL, -- ADD/UPDATE/DELETE/NOOP
    candidate_fact TEXT NOT NULL,
    existing_memory_content TEXT,
    similarity_score FLOAT,
    confidence_score FLOAT,
    reasoning TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);
```

**Evolution Insights** (`memory_master.evolution_insights`):
```sql
CREATE TABLE memory_master.evolution_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES memory_master.users(id),
    app_id UUID REFERENCES memory_master.apps(id),
    date DATE NOT NULL,
    total_operations INTEGER DEFAULT 0,
    add_operations INTEGER DEFAULT 0,
    update_operations INTEGER DEFAULT 0,
    delete_operations INTEGER DEFAULT 0,
    noop_operations INTEGER DEFAULT 0,
    learning_efficiency FLOAT,
    conflict_resolution_count INTEGER DEFAULT 0,
    average_confidence FLOAT,
    average_similarity FLOAT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, app_id, date)
);
```

### Relationships and Constraints

- **Users → Apps**: One-to-many (user can own multiple apps)
- **Users → Memories**: One-to-many (user can have multiple memories)
- **Apps → Memories**: One-to-many (app can create multiple memories)
- **Memories → Evolution Operations**: One-to-many (memory can have multiple operations)
- **Cascade Deletes**: User deletion cascades to apps, memories, and evolution data

## Folder Structure

```
memory-master-v2/
├── api/                           # Backend API & MCP Server
│   ├── app/                      # FastAPI application
│   │   ├── __init__.py
│   │   ├── config.py             # Configuration management
│   │   ├── database.py           # Database connection
│   │   ├── models.py             # SQLAlchemy models
│   │   ├── schemas.py            # Pydantic schemas
│   │   ├── mcp_server.py         # MCP server implementation
│   │   ├── memory_service.py     # Memory business logic
│   │   ├── memory_utils.py       # Memory utilities
│   │   ├── health_service.py     # Health monitoring
│   │   ├── auth/                 # Authentication
│   │   │   ├── middleware.py     # Auth middleware
│   │   │   └── supabase.py       # Supabase auth
│   │   ├── routers/              # API route handlers
│   │   │   ├── memories.py       # Memory endpoints
│   │   │   ├── apps.py           # App management
│   │   │   ├── stats.py          # Analytics
│   │   │   ├── config.py         # Configuration
│   │   │   └── auth.py           # Authentication
│   │   ├── services/             # Business logic services
│   │   │   └── evolution_service.py # Evolution intelligence
│   │   ├── utils/                # Utility functions
│   │   │   ├── memory.py         # Memory client utilities
│   │   │   ├── evolution_prompts.py # Custom prompts
│   │   │   ├── config_manager.py # Configuration management
│   │   │   └── permissions.py    # Access control
│   │   └── database/             # Database services
│   │       ├── base.py           # Base model
│   │       └── service.py        # Database operations
│   ├── alembic/                  # Database migrations
│   │   ├── versions/             # Migration files
│   │   ├── env.py                # Alembic environment
│   │   └── alembic.ini           # Alembic configuration
│   ├── tests/                    # Test suite
│   │   ├── conftest.py           # Test configuration
│   │   ├── test_evolution_*.py   # Evolution tests
│   │   └── test_*.py             # Other tests
│   ├── Dockerfile                # API container
│   ├── main.py                   # FastAPI entry point
│   └── requirements.txt          # Python dependencies
├── ui/                           # Frontend Web Dashboard
│   ├── app/                      # Next.js app directory
│   │   ├── layout.tsx            # Root layout
│   │   ├── page.tsx              # Home page
│   │   ├── dashboard/            # Dashboard pages
│   │   ├── memories/             # Memory management
│   │   ├── apps/                 # App management
│   │   └── settings/             # Configuration
│   ├── components/               # React components
│   │   ├── ui/                   # Base UI components
│   │   ├── memory/               # Memory components
│   │   ├── app/                  # App components
│   │   └── charts/               # Analytics charts
│   ├── hooks/                    # Custom React hooks
│   ├── lib/                      # Utility libraries
│   ├── store/                    # State management
│   ├── styles/                   # CSS and styling
│   ├── Dockerfile                # UI container
│   ├── package.json              # Node.js dependencies
│   └── next.config.mjs           # Next.js configuration
├── backup-scripts/               # Automated backup system
├── docker-compose.yml            # Multi-container orchestration
├── README.md                     # Project overview
├── UserManual.md                 # User documentation
└── DeveloperManual.md            # This file
```

## Development Setup

### Local Development Environment

1. **Prerequisites**:
   ```bash
   # Required
   docker --version          # 20.10+
   docker-compose --version  # 1.29+
   python --version          # 3.12+
   node --version            # 18+
   
   # Optional for local development
   pnpm --version            # 8+
   ```

2. **Environment Configuration**:
   ```bash
   # Copy environment templates
   cp api/.env.example api/.env
   cp ui/.env.example ui/.env
   
   # Edit with your values
   nano api/.env
   nano ui/.env
   ```

3. **Database Setup**:
   ```bash
   # Start Qdrant vector store
   docker-compose up -d mem0_store
   
   # Run database migrations
   cd api
   alembic upgrade head
   ```

4. **Local API Development**:
   ```bash
   cd api
   python -m venv venv
   source venv/bin/activate  # or venv\Scripts\activate on Windows
   pip install -r requirements.txt
   uvicorn main:app --reload --host 0.0.0.0 --port 8765
   ```

5. **Local UI Development**:
   ```bash
   cd ui
   pnpm install
   pnpm dev
   ```

### Development Tools

**Code Quality**:
- **Linting**: ESLint for TypeScript, flake8 for Python
- **Formatting**: Prettier for TypeScript, black for Python
- **Type Checking**: TypeScript strict mode, mypy for Python

**Testing**:
- **Backend**: pytest with asyncio support
- **Frontend**: Jest with React Testing Library
- **Integration**: Docker-based test environment

**Database**:
- **Migrations**: Alembic for schema changes
- **Seeding**: Custom scripts for test data
- **Backup**: Automated scripts in backup-scripts/

## Core Components

### Memory Service Layer

The `MemoryService` class handles all memory operations:

```python
class MemoryService:
    def add_memory(self, text: str, user_id: str, client_name: str) -> Tuple[bool, str, Optional[str]]
    def search_memories(self, query: str, user_id: str, client_name: str, limit: int = 10) -> Tuple[bool, str, List[Dict]]
    def list_memories(self, user_id: str, client_name: str, limit: int = 50, offset: int = 0) -> Tuple[bool, str, List[Dict]]
```

**Key Features**:
- Automatic text chunking for large inputs
- Vector embedding generation
- Memory state management
- Access control validation

### MCP Server Implementation

The MCP server provides standardized tools for AI applications:

```python
# Available MCP Tools
@mcp.tool()
async def add_memories(text: str) -> str:
    """Store new memories with automatic processing."""

@mcp.tool()
async def search_memory(query: str, limit: int = 10) -> str:
    """Semantic search across stored memories."""

@mcp.tool()
async def list_memories(limit: int = 50, offset: int = 0) -> str:
    """Browse memories with filtering options."""
```

### Configuration Management

Dynamic configuration system with hot-reload capabilities:

```python
class ConfigManager:
    def get_config(self, key: str) -> Any
    def update_config(self, key: str, value: Any) -> bool
    def register_listener(self, callback: Callable) -> None
```

## Evolution Intelligence

### Architecture

The Evolution Intelligence system automatically optimizes memory storage through intelligent operations:

**Components**:
1. **EvolutionService**: Orchestrates evolution operations
2. **Custom Prompts**: Technical domain-optimized prompts
3. **Operation Types**: ADD/UPDATE/DELETE/NOOP decisions
4. **Analytics**: Learning efficiency tracking

### Operation Logic

```python
class EvolutionOperationType(enum.Enum):
    ADD = "ADD"        # Store new information
    UPDATE = "UPDATE"  # Enhance existing memory
    DELETE = "DELETE"  # Remove outdated information
    NOOP = "NOOP"      # No operation needed
```

**Decision Criteria**:
- **Similarity Score**: <0.85 → ADD, >0.85 → consider UPDATE/DELETE/NOOP
- **Conflict Detection**: Contradictory information → DELETE + ADD
- **Redundancy Check**: Duplicate information → NOOP
- **Enhancement**: Complementary information → UPDATE

### Custom Prompts

Technical domain-optimized prompts for better evolution decisions:

```python
TECHNICAL_FACT_EXTRACTION_PROMPT = """
You are an expert technical fact extractor for a developer's memory system.
Extract relevant technical facts that would be valuable for future reference.
Focus on: technologies, frameworks, decisions, preferences, solutions, patterns.
"""

TECHNICAL_UPDATE_MEMORY_PROMPT = """
You are an expert technical memory evolution system.
Decide how to handle new information against existing memories.
Prioritize recent, specific information over old, general information.
"""
```

## API Design

### RESTful Endpoints

**Memory Management**:
```
POST   /api/v1/memories              # Create memory
GET    /api/v1/memories              # List memories
GET    /api/v1/memories/search       # Search memories
GET    /api/v1/memories/{id}         # Get memory
PUT    /api/v1/memories/{id}         # Update memory
DELETE /api/v1/memories/{id}         # Delete memory
```

**App Management**:
```
POST   /api/v1/apps                  # Create app
GET    /api/v1/apps                  # List apps
GET    /api/v1/apps/{id}             # Get app details
PUT    /api/v1/apps/{id}             # Update app
DELETE /api/v1/apps/{id}             # Delete app
```

**Evolution Intelligence**:
```
GET    /api/v1/evolution/metrics     # Get evolution metrics
GET    /api/v1/evolution/monitor     # Monitor evolution activity
GET    /api/v1/evolution/insights    # Get learning insights
```

### Response Formats

**Standard Response**:
```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**Error Response**:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {...}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Testing Strategy

### Test Categories

**Unit Tests**:
- Individual function testing
- Mock external dependencies
- Fast execution (<1s per test)

**Integration Tests**:
- Database operations
- API endpoint testing
- Service layer interactions

**End-to-End Tests**:
- Full workflow testing
- Docker-based environment
- Real database interactions

### Test Structure

```python
# Example test structure
class TestEvolutionService:
    def setup_method(self):
        """Setup test database and fixtures."""
        
    def test_add_operation_creates_record(self):
        """Test that ADD operations are properly recorded."""
        
    def test_update_operation_modifies_existing(self):
        """Test that UPDATE operations modify existing memories."""
        
    def teardown_method(self):
        """Cleanup test data."""
```

### Running Tests

```bash
# Backend tests
cd api
pytest tests/ -v --cov=app

# Frontend tests
cd ui
pnpm test

# Integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## Deployment

### Production Deployment

**Docker Compose Production**:
```yaml
services:
  openmemory-mcp:
    image: mem0/openmemory-mcp:latest
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
```

**Environment Variables**:
```bash
# Required
DATABASE_URL=postgresql://user:pass@host:port/db
OPENAI_API_KEY=sk-...
USER=unique-user-id

# Optional
API_KEY=optional-api-key
MIGRATION_MODE=supabase_only
```

### Monitoring

**Health Checks**:
- `/health` endpoint for API server
- Qdrant dashboard at `:6333/dashboard`
- Database connection monitoring

**Metrics**:
- Memory operation latency
- Evolution intelligence efficiency
- Database query performance
- Vector store utilization

## Known Limitations

### Current Constraints

1. **Text Processing**:
   - Large texts (>2000 words) are automatically chunked
   - mem0 may filter out content deemed "not memorable"
   - Claude Desktop has context inconsistency issues

2. **Performance**:
   - Vector embeddings generation adds latency
   - Large memory collections may slow search
   - Concurrent operations may cause conflicts

3. **Evolution Intelligence**:
   - Requires mem0 v1.1+ for custom prompts
   - Decision accuracy depends on LLM quality
   - May generate false positives for similar content

4. **Scalability**:
   - Single Qdrant instance limits throughput
   - Database connections may become bottleneck
   - Memory usage grows with vector storage

### Workarounds

**Text Length Issues**:
- Use concise, specific memories
- Break large documents into logical chunks
- Add relevant metadata for better retrieval

**Performance Optimization**:
- Enable connection pooling
- Use database indexes effectively
- Implement caching for frequent queries

## Future Roadmap

### Short-term (Next 3 months)

1. **Performance Improvements**:
   - Implement Redis caching layer
   - Optimize database queries
   - Add connection pooling

2. **Enhanced Evolution Intelligence**:
   - Improve decision accuracy
   - Add confidence thresholds
   - Implement conflict resolution strategies

3. **Better MCP Integration**:
   - Support more MCP-compatible tools
   - Improve error handling
   - Add batch operations

### Medium-term (3-6 months)

1. **Multi-tenant Support**:
   - Organization-level memory sharing
   - Role-based access control
   - Team collaboration features

2. **Advanced Analytics**:
   - Memory usage patterns
   - Learning efficiency trends
   - Predictive insights

3. **API Enhancements**:
   - GraphQL support
   - Webhook notifications
   - Bulk operations

### Long-term (6+ months)

1. **Distributed Architecture**:
   - Multiple Qdrant instances
   - Horizontal scaling
   - Load balancing

2. **Advanced AI Features**:
   - Custom embedding models
   - Domain-specific optimizations
   - Automated memory curation

3. **Enterprise Features**:
   - SSO integration
   - Audit logging
   - Compliance tools

## Contributing Guidelines

### Code Standards

**Python (Backend)**:
- Follow PEP 8 style guide
- Use type hints for all functions
- Document with docstrings
- Maximum line length: 100 characters

**TypeScript (Frontend)**:
- Use strict TypeScript configuration
- Follow React best practices
- Use functional components with hooks
- Implement proper error boundaries

### Commit Guidelines

**Commit Message Format**:
```
type(scope): description

[optional body]

[optional footer]
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions/modifications
- `chore`: Maintenance tasks

### Pull Request Process

1. **Create Feature Branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement Changes**:
   - Write tests for new functionality
   - Update documentation
   - Follow code standards

3. **Submit Pull Request**:
   - Provide clear description
   - Link related issues
   - Include test results

4. **Code Review**:
   - Address reviewer feedback
   - Ensure CI passes
   - Update based on suggestions

### Development Rules

**Database Changes**:
- Always create Alembic migrations
- Test migrations on sample data
- Document schema changes

**API Changes**:
- Maintain backward compatibility
- Update API documentation
- Version breaking changes

**Testing Requirements**:
- Minimum 80% code coverage
- All tests must pass
- Include integration tests for new features

---

This developer manual provides the foundation for contributing to Memory Master v2. For specific implementation details, refer to the inline code documentation and test examples.
