# Task ID: 1
# Title: Setup Database Schema for Evolution Configuration
# Status: pending
# Dependencies: None
# Priority: high
# Description: Create database tables to store evolution configuration data, domain settings, custom prompts, and version history
# Details:
Create tables: evolution_configs (id, user_id, domain_type, created_at, updated_at), custom_prompts (id, config_id, prompt_type, content, version, is_active), prompt_versions (id, prompt_id, content, version_number, created_at), domain_settings (id, config_id, similarity_threshold, update_threshold, noop_threshold, content_length_min). Add indexes on user_id, domain_type, and is_active columns. Implement soft deletes for version history.

# Test Strategy:
Create unit tests for all CRUD operations, test foreign key constraints, verify version history tracking, and validate data integrity with concurrent updates
