# Task ID: 8
# Title: Build Domain Configuration Tab
# Status: pending
# Dependencies: 7
# Priority: high
# Description: Create domain selection interface with Technical Development and Business Operations cards and one-click switching
# Details:
Create DomainConfig component with two large visual cards showing domain options. Technical Development card: focus on programming, frameworks, development tools. Business Operations card: focus on customer interactions, business processes, metrics. Add current status indicators, detailed descriptions, and feature lists. Implement one-click switching with confirmation dialog, impact warnings, and automatic prompt backup before switching.

# Test Strategy:
Test domain switching functionality, verify backup creation, test confirmation dialogs, and validate prompt updates after domain changes
